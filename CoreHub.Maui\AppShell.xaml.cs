namespace CoreHub
{
    public partial class AppShell : Shell
    {
        public AppShell()
        {
            InitializeComponent();

            // 注册原生页面路由
            RegisterRoutes();
        }

        private void RegisterRoutes()
        {
            try
            {
                // 注册更新页面路由
                Routing.RegisterRoute("update", typeof(Platforms.Android.UpdatePage));
                System.Diagnostics.Debug.WriteLine("已注册更新页面路由: update -> UpdatePage");

                // 可以在这里注册其他原生页面路由
                // Routing.RegisterRoute("splash", typeof(SplashPage));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"注册路由时发生错误: {ex}");
            }
        }
    }
}
