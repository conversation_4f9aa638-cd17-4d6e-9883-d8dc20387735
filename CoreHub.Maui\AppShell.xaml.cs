namespace CoreHub
{
    public partial class AppShell : Shell
    {
        public AppShell()
        {
            InitializeComponent();

            // 注册原生页面路由
            RegisterRoutes();
        }

        private void RegisterRoutes()
        {
            // 注册更新页面路由
            Routing.RegisterRoute("update", typeof(Platforms.Android.UpdatePage));

            // 可以在这里注册其他原生页面路由
            // Routing.RegisterRoute("splash", typeof(SplashPage));
        }
    }
}
