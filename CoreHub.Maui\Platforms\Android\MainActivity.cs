﻿using Android.App;
using Android.Content.PM;
using Android.OS;
using Android.Content;
using CoreHub.Shared.Services;

namespace CoreHub
{
    [Activity(Theme = "@style/Maui.SplashTheme", MainLauncher = true, LaunchMode = LaunchMode.SingleTop, ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density)]
    public class MainActivity : MauiAppCompatActivity
    {
        protected override void OnCreate(Bundle? savedInstanceState)
        {
            try
            {
                base.OnCreate(savedInstanceState);

                // 检查是否是从更新通知点击进入的
                CheckUpdateNotificationIntent();
            }
            catch (System.Exception ex)
            {
                // 记录启动错误
                System.Diagnostics.Debug.WriteLine($"MainActivity OnCreate Error: {ex}");

                // 可以在这里添加更详细的错误处理
                // 比如显示错误对话框或重启应用
                throw;
            }
        }

        protected override void OnNewIntent(Intent? intent)
        {
            base.OnNewIntent(intent);
            Intent = intent;
            CheckUpdateNotificationIntent();
        }

        private void CheckUpdateNotificationIntent()
        {
            try
            {
                if (Intent?.GetBooleanExtra("show_update", false) == true)
                {
                    // 延迟一下，等待应用完全加载和Shell初始化
                    Task.Run(async () =>
                    {
                        try
                        {
                            // 等待更长时间确保Shell完全初始化
                            await Task.Delay(3000);

                            // 确保在主线程中执行
                            await MainThread.InvokeOnMainThreadAsync(async () =>
                            {
                                // 等待Shell可用
                                var maxRetries = 10;
                                var retryCount = 0;

                                while (retryCount < maxRetries)
                                {
                                    try
                                    {
                                        if (Shell.Current != null)
                                        {
                                            // 获取更新服务并显示更新页面
                                            var updateService = IPlatformApplication.Current?.Services?.GetService<IClientUpdateService>();
                                            if (updateService is Platforms.Android.AndroidUpdateService androidUpdateService)
                                            {
                                                await androidUpdateService.ShowUpdatePageAsync();
                                                return; // 成功后退出
                                            }
                                        }
                                    }
                                    catch (System.Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"尝试显示更新页面失败 (第{retryCount + 1}次): {ex.Message}");
                                    }

                                    retryCount++;
                                    await Task.Delay(500); // 等待500ms后重试
                                }

                                System.Diagnostics.Debug.WriteLine("显示更新页面失败：超过最大重试次数");
                            });
                        }
                        catch (System.Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"显示更新页面时发生错误: {ex}");
                        }
                    });
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理更新通知Intent时发生错误: {ex}");
            }
        }
    }
}
