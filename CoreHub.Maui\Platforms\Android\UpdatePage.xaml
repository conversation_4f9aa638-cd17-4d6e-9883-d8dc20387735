<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CoreHub.Platforms.Android.UpdatePage"
             Title="应用更新"
             BackgroundColor="#F5F5F5">

    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <!-- 更新图标 -->
            <Frame BackgroundColor="White"
                   CornerRadius="10"
                   HasShadow="True"
                   Padding="15">
                <StackLayout Orientation="Horizontal" Spacing="15">
                    <Image Source="update_icon.png"
                           HeightRequest="50"
                           WidthRequest="50"
                           VerticalOptions="Center"/>
                    <Label Text="发现新版本"
                           FontSize="20"
                           FontAttributes="Bold"
                           VerticalOptions="Center"
                           TextColor="#333333"/>
                </StackLayout>
            </Frame>

            <!-- 版本信息 -->
            <Frame BackgroundColor="White" 
                   CornerRadius="10" 
                   HasShadow="True"
                   Padding="20">
                <StackLayout Spacing="10">
                    <Label Text="版本信息" 
                           FontSize="18" 
                           FontAttributes="Bold"
                           TextColor="#333333"/>
                    
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto,Auto" RowSpacing="8">
                        <Label Grid.Row="0" Grid.Column="0" Text="当前版本：" TextColor="#666666"/>
                        <Label Grid.Row="0" Grid.Column="1" x:Name="CurrentVersionLabel" Text="-" TextColor="#333333"/>
                        
                        <Label Grid.Row="1" Grid.Column="0" Text="最新版本：" TextColor="#666666"/>
                        <Label Grid.Row="1" Grid.Column="1" x:Name="LatestVersionLabel" Text="-" TextColor="#007ACC" FontAttributes="Bold"/>
                        
                        <Label Grid.Row="2" Grid.Column="0" Text="文件大小：" TextColor="#666666"/>
                        <Label Grid.Row="2" Grid.Column="1" x:Name="FileSizeLabel" Text="-" TextColor="#333333"/>
                        
                        <Label Grid.Row="3" Grid.Column="0" Text="发布时间：" TextColor="#666666"/>
                        <Label Grid.Row="3" Grid.Column="1" x:Name="ReleaseDateLabel" Text="-" TextColor="#333333"/>
                    </Grid>
                </StackLayout>
            </Frame>

            <!-- 更新说明 -->
            <Frame BackgroundColor="White" 
                   CornerRadius="10" 
                   HasShadow="True"
                   Padding="20"
                   x:Name="ReleaseNotesFrame">
                <StackLayout Spacing="10">
                    <Label Text="更新说明" 
                           FontSize="18" 
                           FontAttributes="Bold"
                           TextColor="#333333"/>
                    <Label x:Name="ReleaseNotesLabel" 
                           Text="正在加载更新说明..."
                           TextColor="#666666"
                           LineBreakMode="WordWrap"/>
                </StackLayout>
            </Frame>

            <!-- 强制更新提示 -->
            <Frame BackgroundColor="#FFF3CD" 
                   BorderColor="#FFEAA7" 
                   CornerRadius="10" 
                   HasShadow="True"
                   Padding="15"
                   x:Name="ForceUpdateFrame"
                   IsVisible="False">
                <StackLayout Orientation="Horizontal" Spacing="10">
                    <Label Text="⚠️" FontSize="20" VerticalOptions="Center"/>
                    <Label Text="这是一个强制更新，必须更新后才能继续使用应用。" 
                           TextColor="#856404"
                           VerticalOptions="Center"
                           LineBreakMode="WordWrap"
                           HorizontalOptions="FillAndExpand"/>
                </StackLayout>
            </Frame>

            <!-- 下载进度 -->
            <Frame BackgroundColor="White" 
                   CornerRadius="10" 
                   HasShadow="True"
                   Padding="20"
                   x:Name="ProgressFrame"
                   IsVisible="False">
                <StackLayout Spacing="15">
                    <Label Text="下载进度" 
                           FontSize="18" 
                           FontAttributes="Bold"
                           TextColor="#333333"/>
                    
                    <ProgressBar x:Name="DownloadProgressBar" 
                                 Progress="0" 
                                 ProgressColor="#007ACC"
                                 HeightRequest="8"/>
                    
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Grid.Column="0" 
                               x:Name="ProgressLabel" 
                               Text="准备下载..." 
                               TextColor="#666666"/>
                        <Label Grid.Column="1" 
                               x:Name="ProgressPercentLabel" 
                               Text="0%" 
                               TextColor="#007ACC"
                               FontAttributes="Bold"/>
                    </Grid>
                    
                    <Label x:Name="DownloadSpeedLabel" 
                           Text="" 
                           TextColor="#999999" 
                           FontSize="12"
                           HorizontalOptions="Center"/>
                </StackLayout>
            </Frame>

            <!-- 操作按钮 -->
            <StackLayout Spacing="10" x:Name="ButtonContainer">
                <Button x:Name="UpdateButton"
                        Text="立即更新"
                        BackgroundColor="#007ACC"
                        TextColor="White"
                        FontSize="16"
                        FontAttributes="Bold"
                        HeightRequest="50"
                        CornerRadius="25"
                        Clicked="OnUpdateClicked"/>

                <Button x:Name="CancelButton"
                        Text="稍后提醒"
                        BackgroundColor="#F0F0F0"
                        TextColor="#666666"
                        FontSize="14"
                        HeightRequest="45"
                        CornerRadius="22"
                        BorderColor="#CCCCCC"
                        BorderWidth="1"
                        Clicked="OnCancelClicked"/>
            </StackLayout>

        </StackLayout>
    </ScrollView>

</ContentPage>
